<?php

namespace Vbo\Settings\Classes;

class ErrorMail
{

/**
 * Build a raw SQL INSERT statement for the `leads` table from a mapped data array.
 *
 * @param  array  $mappedLead  Associative array: column => value
 *
 * @return string
 */
public function buildLeadInsertStatement(array $mappedLead): string
{
    if (empty($mappedLead)) {
        return '';
    }

    $rawInput = $mappedLead;

    $keyMap = [
        'io_uip'           => 'ip',
        'io_uid'           => 'uid',
        'origin'           => 'lead_origin',
        'Website'          => 'website',
        'lopende_leningen' => 'has_lopende_leningen',
    ];

    foreach ($keyMap as $from => $to) {
        if (array_key_exists($from, $rawInput) && !array_key_exists($to, $mappedLead)) {
            $mappedLead[$to] = $rawInput[$from];
        }
    }

    if (!empty($rawInput['voornaam'])) {
        $fullName = !empty($rawInput['naam'])
            ? trim((string) $rawInput['voornaam'] . ' ' . (string) $rawInput['naam'])
            : trim((string) $rawInput['voornaam']);
        if ($fullName !== '') {
            $mappedLead['naam'] = $fullName;
        }
    }

    $allowedColumns = [
        'website_id', 'messagebird_id', 'messagebird_status', 'messagebird_update', 'messagebird_fails', 'mandrill_id', 'mandrill_status',
        'mandrill_scheduled_time',
        'newsletter', 'inserted', 'updated_at', 'invoice_sent', 'locken_cc_pakket', 'lead_id', 'lead_type', 'partner_id', 'referer', 'referer_gerustleven',
        'query_string', 'telefoonnr', 'best_reached', 'naam', 'factor', 'status', 'uitgeleverd_aan', 'uitgeleverd_aan_override', 'uitgeleverd_aan_callcenter',
        'opmerking_retour', 'rabo', 'maandinkomen', 'aankoopsom', 'Overwaarde', 'Eigen_vermogen', 'Borg', 'has_lopende_leningen', 'Goedkoper_wonen', 'ip',
        'kw_info', 'marktwaarde_woning', 'reden', 'date_interest_period_end', 'partner', 'maandinkomen2', 'geslacht', 'initialen', 'Geboortedatum_dag',
        'Geboortedatum_maand', 'Geboortedatum_jaar', 'straat', 'huisnr', 'postcode', 'woonplaats', 'country', 'email', 'tel', 'tel_country_code',
        'opmerkingen', 'locken', 'cc_result', 'gg_date', 'cntcalls', 'prio', 'tb_date', 'tb_time', 'mutated_by', 'optin_vink', 'bkr', '_cc_opmerkingen',
        '_cc_afspraakdatum', 'hasDistance', 'ALS', 'afgekeurd_op', 'is_double', 'haalbaar', 'haalbaar_op_hypotheek_tov_waarde_woning', 'haalbaar_nieuw',
        'haalbaar_gh_nieuw', 'haalbaar_nieuw_detail', 'leeftijd', 'first_house', 'info_engine', 'info_source', 'info_keyword', 'info_referer',
        'info_request_uri', 'address_valid', 'is_valid_phone', 'provincie', 'dienstverband', 'dienstverband_partner', 'voorkeur_periode_rentevast',
        'website', 'leadherkomst', 'opmerkingen_klant', 'rente_gecommuniceerd', 'Geboortedatum_partner_dag', 'Geboortedatum_partner_maand',
        'Geboortedatum_partner_jaar', 'address_valid_flags', 'locked_by_user_id', 'locked_until_date', 'old_street', 'old_zipcode', 'old_city',
        'old_province', 'manual_approved', 'trap_renovatie', 'energie', 'is_afsprakenmanager', 'double_score', 'cc_is_used', 'unsubscribed', 'lead_origin',
        'duplicate_reason', 'nbc_woonsituatie', 'nbc_verwachte_aankoopprijs', 'nbc_huidige_waarde', 'nbc_huidige_geldverstrekker', 'nbc_nhg',
        'nbc_hypotheek_start', 'nbc_hypotheek_now', 'nbc_hypotheek_rente', 'nbc_ingang_hypotheek', 'nbc_einde_rentevast', 'nbc_type_hypotheek',
        'nbc_bruto_toekomstig_pensioeninkomen', 'nbc_bruto_pensioeninkomen', 'nbc_bruto_toekomstig_pensioeninkomen_partner',
        'nbc_bruto_pensioeninkomen_partner',
        'nbc_geboortedatum_partner', 'nbc_hypotheek_voor_2013', 'nbc_openstaande_kredietsaldo', 'nbc_bruto_hypotheeklast', 'nbc_woonlast', 'nbc_reden2',
        'nbc_reden3', 'nbc_reden4', 'nbc_reden5', 'nbc_reden6', 'nbc_reden7', 'nbc_reden8', 'nbc_funding_amount', 'besparing', 'haalbaar_nat', 'broninkomen',
        'broninkomen_partner', 'validmail', 'double_checked',
    ];

    $mappedLead = array_intersect_key($mappedLead, array_flip($allowedColumns));

    if (!array_key_exists('inserted', $mappedLead)) {
        $mappedLead['inserted'] = null;
    }

    if (!array_key_exists('updated_at', $mappedLead)) {
        $mappedLead['updated_at'] = null;
    }

    if (empty($mappedLead)) {
        return '';
    }

    $mappedLead['lead_id'] = "MISSING " . substr((string) time(), -5);

    $numericColumns = [
        'address_valid_flags',
        'locked_by_user_id',
        'manual_approved',
        'trap_renovatie',
        'energie',
        'double_score',
        'is_double',
        'double_checked',
        'haalbaar_nieuw',
        'haalbaar_gh_nieuw',
        'address_valid',
        'is_valid_phone',
        'cc_is_used',
        'unsubscribed',
        'has_lopende_leningen',
        'newsletter',
        'invoice_sent',
        'website_id',
        'partner_id',
        'lead_type',
    ];

    $columns = array_keys($mappedLead);

    $keys = '`' . implode('`, `', $columns) . '`';


    $values = [];

    foreach ($columns as $column) {
        $value = $mappedLead[$column];

        if ($column === 'updated_at' || $column === 'inserted') {
            $values[] = 'NOW()';
            continue;
        }

        if ($value === null) {
            $values[] = 'NULL';
            continue;
        }

        if (in_array($column, $numericColumns, true)) {
            $values[] = is_numeric($value) ? (string) $value : '0';
            continue;
        }

        $string   = (string) $value;
        $escaped  = str_replace("'", "''", $string);
        $values[] = "'{$escaped}'";
    }

    $valuesSql = implode(', ', $values);

    return "INSERT INTO `leads` ({$keys}) VALUES ({$valuesSql});";
}
}
