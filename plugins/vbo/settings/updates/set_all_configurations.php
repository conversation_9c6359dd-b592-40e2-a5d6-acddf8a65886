<?php namespace Vbo\Settings\Updates;

use October\Rain\Database\Updates\Seeder;
use <PERSON>V<PERSON>ce\SmallGDPR\Models\CookiesSettings;
use Renatio\FormBuilder\Models\Form;
use Renatio\FormBuilder\Models\FieldType;
use Config;

/**
 * Consolidated configuration seeder for VBO Settings Plugin v2.0
 * This seeder includes all configuration updates from versions 1.0.1 through 1.3.3
 */
class SetAllConfigurations extends Seeder
{
    public function run()
    {
        // Set cookie settings
        $this->setCookieSettings();

        // Update default forms
        $this->updateDefaultForms();

        // Update fieldtype configurations
        $this->updateFieldTypeConfigurations();
    }

    private function setCookieSettings()
    {
        // From set_cookie_settings.php
        // Only set if not already configured
        if (!CookiesSettings::get('cookies_bar_disable_page_reload')) {
            CookiesSettings::set('cookies_bar_disable_page_reload', '1');
        }

        // From set_cookie_settings_2.php and set_cookie_settings_3.php
        // Only set default values if not already configured
        if (!CookiesSettings::get('cookies_bar_text')) {
            CookiesSettings::set('cookies_bar_text', 'Deze website gebruikt cookies om de gebruikerservaring te verbeteren. Door gebruik te maken van onze website ga je akkoord met alle cookies in overeenstemming met ons cookiebeleid.');
        }
        if (!CookiesSettings::get('cookies_bar_button_accept_text')) {
            CookiesSettings::set('cookies_bar_button_accept_text', 'Accepteren');
        }
        if (!CookiesSettings::get('cookies_bar_button_manage_text')) {
            CookiesSettings::set('cookies_bar_button_manage_text', 'Beheren');
        }
        if (!CookiesSettings::get('cookies_manage_title')) {
            CookiesSettings::set('cookies_manage_title', 'Cookie instellingen');
        }
        if (!CookiesSettings::get('cookies_manage_text')) {
            CookiesSettings::set('cookies_manage_text', 'Hier kun je je cookie voorkeuren instellen. Je kunt verschillende categorieën in- of uitschakelen. Voor meer informatie over cookies en welke gegevens we verzamelen, lees ons cookiebeleid.');
        }
        if (!CookiesSettings::get('cookies_manage_button_save')) {
            CookiesSettings::set('cookies_manage_button_save', 'Opslaan');
        }
        if (!CookiesSettings::get('cookies_manage_button_accept_all')) {
            CookiesSettings::set('cookies_manage_button_accept_all', 'Alles accepteren');
        }
    }

    private function updateDefaultForms()
    {
        $app_name = Config::get('app.name');

        // Update newsletter form - only set if not already configured
        $newsletter = Form::where('code', 'nieuwsbrief')->first();
        if ($newsletter) {
            if (empty($newsletter->from_email)) {
                $newsletter->from_email = "<EMAIL>";
            }
            if (empty($newsletter->from_name)) {
                $newsletter->from_name = $app_name;
            }
            if (empty($newsletter->template_code)) {
                $newsletter->template_code = "vbo.settings::mail.newsletter";
            }
            $newsletter->save();
        }

        // Update partner form - only set if not already configured
        $partner = Form::where('code', 'contact-form-2')->first();
        if ($partner) {
            if (empty($partner->from_email)) {
                $partner->from_email = "<EMAIL>";
            }
            if (empty($partner->from_name)) {
                $partner->from_name = $app_name;
            }
            if (empty($partner->template_code)) {
                $partner->template_code = "vbo.settings::mail.partner";
            }
            $partner->save();
        }

        // Update all forms to use proper template
        $forms = Form::whereNotIn('code', ['nieuwsbrief', 'contact-form-2'])->get();
        foreach ($forms as $form) {
            if (empty($form->template_code)) {
                $form->template_code = "renatio.formbuilder::mail.notification";
                $form->save();
            }
        }
    }

    private function updateFieldTypeConfigurations()
    {
        // Update specific fieldtypes with enhanced configurations
        $this->updatePhoneNumberFieldTypes();
        $this->updateAddressFieldTypes();
        $this->updateFormControlFieldTypes();
    }

    private function updatePhoneNumberFieldTypes()
    {
        // Update telefoonnummer fieldtype - only if name is not customized
        $phoneField = FieldType::where('code', 'text-6')->first();
        if ($phoneField && ($phoneField->name == 'text-6' || empty($phoneField->name))) {
            $phoneField->name = 'Telefoonnummer (NL)';
            $phoneField->save();
        }

        // Update international phone fieldtype - only if name is not customized
        $intPhoneField = FieldType::where('code', 'telephone-int')->first();
        if ($intPhoneField && ($intPhoneField->name == 'telephone-int' || empty($intPhoneField->name))) {
            $intPhoneField->name = 'Telefoonnummer (Internationaal)';
            $intPhoneField->save();
        }
    }

    private function updateAddressFieldTypes()
    {
        // Update street/city fieldtype - only if name is not customized
        $streetCityField = FieldType::where('code', 'straat-plaats')->first();
        if ($streetCityField && ($streetCityField->name == 'straat-plaats' || empty($streetCityField->name))) {
            $streetCityField->name = 'Straat + Plaats';
            $streetCityField->save();
        }

        // Update housenumber fieldtype - only if name is not customized
        $houseNumberField = FieldType::where('code', 'huisnummer')->first();
        if ($houseNumberField && ($houseNumberField->name == 'huisnummer' || empty($houseNumberField->name))) {
            $houseNumberField->name = 'Huisnummer';
            $houseNumberField->save();
        }
    }

    private function updateFormControlFieldTypes()
    {
        // Update submit button fieldtype - only if name is not customized
        $submitField = FieldType::where('code', 'submit')->first();
        if ($submitField && ($submitField->name == 'submit' || empty($submitField->name))) {
            $submitField->name = 'Verzend knop';
            $submitField->save();
        }

        // Update section fieldtype - only if name is not customized
        $sectionField = FieldType::where('code', 'section')->first();
        if ($sectionField && ($sectionField->name == 'section' || empty($sectionField->name))) {
            $sectionField->name = 'Sectie';
            $sectionField->save();
        }

        // Update upload fieldtype - only if name is not customized
        $uploadField = FieldType::where('code', 'upload')->first();
        if ($uploadField && ($uploadField->name == 'upload' || empty($uploadField->name))) {
            $uploadField->name = 'Bestand upload';
            $uploadField->save();
        }
    }
}
