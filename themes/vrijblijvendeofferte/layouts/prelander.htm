description = "Prelander"

[staticPage]
useContent = 0
default = 0

[staticBreadcrumbs]

[KlantenvertellenScore]

[KlantenvertellenFeed]

[KlantenvertellenFeed klantenvertellenFormFeed]

[renderForm NewsletterForm]
formCode = "nieuwsbrief"

[renderForm PartnerForm2]
formCode = "contact-form-2"

[categories]

[cookiesBar]

[Testing]

[SiteSettings]
==
<?php
use Vbo\Settings\Classes\Visitor;
use Vbo\Settings\Classes\ValidateMail;
function onStart()
{
    $this['zipcode'] = Session::get('zipcode');
    $this['housenumber'] = Session::get('housenumber');
    $this['addition'] = Session::get('addition');
    $this['streetname'] = Session::get('streetname');
    $this['city'] = Session::get('city');

    $this['landingpage'] = Request::server('HTTP_HOST') . Request::server('REQUEST_URI');
    $this['ua_code'] = Request::get('ua');
    Session::put('landingpage', Request::server('HTTP_HOST') . Request::server('REQUEST_URI'));
    Session::put('uri', str_replace(Request::url(), '', Request::fullUrl()));
    Session::put('landingpage_first', Request::server('HTTP_HOST') . Request::server('REQUEST_URI'));
    Session::put('ua_code', Request::get('ua'));
    Session::put('prelander', true);

    $parsedUrl = explode(".", parse_url(URL::current(), PHP_URL_HOST));
    $this['domainTld'] = end($parsedUrl);
    $this['uri'] = Session::get('uri');
}
function onEnd()
{
    $visitor = new Visitor;
    $this['uid_code'] = $visitor->getVisitor();
}
function onCheckMail()
{
    $checkMail = new ValidateMail;
    $validmail = $checkMail->check(post('email'));
    return $validmail;
}
?>
==
<!doctype html>
<html lang="{{ site_lang|default('nl') }}" class="scroll-smooth">
    <head>
        <script>var domainTld = "{{ domainTld }}";</script>
        {% partial "site/head" %}
        {% styles %}
    </head>

    {variable name="page_title" label="Pagina titel" tab="Header" type="richeditor" size="small" toolbarButtons="color|paragraphFormat|fontSize|html|clearFormatting|undo|redo"}{/variable}
    {variable name="header_text_intro" label="Introductie tekst" tab="Header" type="richeditor" toolbarButtons="color|paragraphFormat|fontSize|html|clearFormatting|undo|redo"}{/variable}
    {variable name="header_text" label="Header tekst" tab="Header" type="richeditor" toolbarButtons="color|paragraphFormat|fontSize|html|clearFormatting|undo|redo"}{/variable}
    {variable name="header_color" tab="Header" type="colorpicker" label="Header kleur" allowEmpty="true" availableColors="#16588d|#00abf0"}{/variable}
    {variable name="arrow_color" tab="Header" type="colorpicker" label="Pijl kleur" allowEmpty="true" availableColors="#16588d|#00abf0"}{/variable}
    {variable name="form_color" tab="Header" type="colorpicker" label="Formulier kleur" allowEmpty="true" availableColors="#16588d|#00abf0"}{/variable}
    {variable name="disable_header_illustration" label="Verberg header illustratie" tab="Header" type="switch"}{/variable}
    {variable name="header_illustration" label="Header illustratie" tab="Header" type="mediafinder" mode="image"}{/variable}
    {variable name="header_background_image" label="Header achtergrond afbeelding" tab="Header" type="mediafinder" mode="image"}{/variable}
    {variable name="header_bg_image_overlay" span="auto" tab="Header" type="colorpicker" label="Header achtergrond overlay" allowEmpty="true" availableColors="#000000|#ffffff"}{/variable}
    {variable name="header_bg_image_overlay_alpha" span="auto" cssClass="text-left" tab="Header" type="number" label="Header achtergrond overlay transparantie" comment="0 = transparant, 100 = ondoorzichtig" step="10" min="0" max="100"}{/variable}
    {variable name="header_bg_position" label="Achtergrond positie" tab="Header" type="dropdown" default="center" options="center:Midden|left:Links|right:Rechts|top:Boven|bottom:Onder|top-right:Boven + Rechts|bottom-right:Onder + Rechts|top-left:Boven + Links|bottom-left:Onder + Links"}{/variable}

    {variable name="formCode" label="Kies een formulier" tab="Formulier" type="dropdown" options="\Vbo\Settings\Plugin::getFormCodeOptions"}{/variable}
    {variable name="textUnderForm" label="Tekst onder verstuurknop" tab="Formulier" type="text" placeholder="In 2 minuten bent u klaar"}{/variable}
    {variable name="veiligeVerbinding" label="Toon veilige verbinding tekst" tab="Formulier" type="switch"}{/variable}

    {variable name="page_setting_no_nav" label="Geen navigatie tonen" tab="Pagina instellingen" type="switch"}{/variable}
    {variable name="hide_reviews" label="Geen reviews tonen" tab="Pagina instellingen" type="switch"}{/variable}
    {variable name="page_setting_logo_unclickable" label="Logo niet aanklikbaar maken" tab="Pagina instellingen" type="switch"}{/variable}
    {variable name="hide_lenen_banner" label="Geld Lenen Kost Geld banner niet tonen" tab="Pagina instellingen" type="switch"}{/variable}
    {variable name="hide_whatsapp_button" label="Whatsapp button niet tonen" tab="Pagina instellingen" type="switch"}{/variable}


    <body class="font-barlow antialiased prelander">
        {% partial "site/scripts_body_top" %}

        {% if not hide_lenen_banner %}
            {% if this.theme.geld_lenen_banner %}
            <div class="py-2 bg-white flex w-full justify-center px-4 md:px-8">
                <img src="{{ this.theme.geld_lenen_banner|media }}" alt="Let op: Geld lenen kost geld" class="max-w-full">
            </div>
            {% endif %}
        {% endif %}


        {% if page_setting_no_nav or this.theme.site_hide_nav %}
            {% partial "page/navbar_no_menu" %}
        {% else %}
            {% partial "page/navbar" %}
        {% endif %}

        {repeater tab="Content boven header" name="flexibleTop" prompt="Blok toevoegen" groups="$/../themes/vrijblijvendeofferte/repeaters/sections.yaml" displayMode="builder" titleFrom="name"}
            {% partial "section/" ~ fields._group ~ ".htm" fields=fields %}
        {/repeater}
        {% set bgPos = "bg-center" %}
        {% if header_bg_position == 'left' %}
            {% set bgPos = "bg-left" %}
        {% elseif header_bg_position == 'right' %}
            {% set bgPos = "bg-right" %}
        {% elseif header_bg_position == 'top' %}
            {% set bgPos = "bg-top" %}
        {% elseif header_bg_position == 'bottom' %}
            {% set bgPos = "bg-bottom" %}
        {% elseif header_bg_position == 'top-right' %}
            {% set bgPos = "bg-right-top" %}
        {% elseif header_bg_position == 'bottom-right' %}
            {% set bgPos = "bg-right-bottom" %}
        {% elseif header_bg_position == 'top-left' %}
            {% set bgPos = "bg-left-top" %}
        {% elseif header_bg_position == 'bottom-left' %}
            {% set bgPos = "bg-left-bottom" %}
        {% endif %}
        {% if header_background_image %}
            {% set headerImage = header_background_image|media|resize(1920, null, { extension: 'webp', filename: true, quality: 90 }) %}
            <link rel="preload" as="image" href="{{ headerImage }}" fetchpriority="high" />
            {% set bgImage = "background-image: url('" ~ headerImage ~ "');" %}
        {% endif %}
        <header class="pt-9 md:pt-12 lg:pt-16 bg-secondary-500 bg-no-repeat relative {{ bgPos }} bg-cover" style="{% if header_color %}background-color: {{ header_color }};{% endif %} {{ bgImage ? bgImage }}">
            {# BG Image overlay #}
            {% if bgImage %}
                <div class="absolute inset-0" style="background-color: {{ header_bg_image_overlay }}; opacity: {{ header_bg_image_overlay_alpha }}%;"></div>
            {% endif %}

            <div class="container relative">
                <div class="font-bold tracking-tight leading-normal custom_heading">{{ page_title|raw }}</div>
                <div class="w-14 h-1 bg-primary-500 my-3 md:my-6"></div>

                <div class="grid md:grid-cols-10">
                    <div class="md:col-span-6 md:pr-16">
                        <div class="md:flex md:flex-col md:h-full">
                            <div class="text-xl mb-12 lg:mb-0">
                                <div class="text-white basic_content text-xl md:text-2xl">{{ header_text|raw }}</div>
                                <div class="text-white mt-4 md:mt-6 text-xl md:text-2xl">{{ header_text_intro|raw }}</div>
                                <div class="hidden md:flex md:justify-end">
                                    {# <img src="{{ 'assets/img/arrow_header_prelander.png'|theme }}" alt="" class="rotate-180 ml-16 mt-8 md:rotate-0 md:ml-0 md:mt-0"> #}
                                    <svg width="107px" height="31px" viewBox="0 0 107 31" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" class="rotate-180 ml-16 mt-8 md:rotate-0 md:ml-0 md:mt-0" style="color: {{ arrow_color }};">
                                        <g id="Page-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" stroke-linecap="round">
                                            <g id="Prelander_OV_desktop_07-12-22" transform="translate(-723.000000, -600.000000)" stroke="currentColor" stroke-width="2">
                                                <g id="Hero-" transform="translate(0.000000, 192.000000)">
                                                    <g id="Pijl" transform="translate(778.006012, 424.498722) scale(1, -1) rotate(-168.000000) translate(-778.006012, -424.498722) translate(724.999866, 413.998722)">
                                                        <g id="Group-9" transform="translate(0.000000, 0.000000)">
                                                            <path d="M106.012293,2.84217094e-14 C96.7805153,12.4934872 81.6000933,19.398895 60.4710267,20.7162232 C28.7774269,22.6922155 10.3665658,13.9139417 2.01229284,7.86061113" id="Path-4"></path>
                                                            <polyline id="Path-6" stroke-linejoin="round" transform="translate(8.173184, 10.998406) rotate(-15.000000) translate(-8.173184, -10.998406) " points="1.54688021 17.8474616 1.55960669 4.14935031 14.7994884 8.14768203"></polyline>
                                                        </g>
                                                    </g>
                                                </g>
                                            </g>
                                        </g>
                                    </svg>
                                </div>
                            </div>
                            {% if not disable_header_illustration %}
                                <div class="mt-auto hidden md:block">
                                    {% if header_illustration %}
                                        <img src="{{ header_illustration | media }}" alt="">
                                    {% else %}
                                        <img src="{{ 'assets/img/header-illustration-houses.png'|theme }}" alt="Illustratie met huisjes">
                                    {% endif %}
                                </div>
                            {% endif %}

                        </div>
                    </div>
                    <div class="md:col-span-4">
                        <div class="bg-white rounded-lg shadow-xl p-6 {{ veiligeVerbinding ? 'pb-20 md:p-12 md:pb-20' : 'pb-12 md:p-12 md:pb-20' }} overflow-hidden mb-6 md:mb-16 lg:mb-32 relative">



                            <div id="form_default" class="form_prelander mb-6">

                                {% ajaxPartial 'site/dynamic-form' formcode=formCode %}

                            </div>



                            {% if form_color %}
                                <style>
                                    :root {
                                        --prelander-primary: {{ form_color }}
                                    }
                                    .steps .step.active, .steps .step.current.active {
                                        border-color: var(--prelander-primary);
                                        color: var(--prelander-primary);
                                    }
                                    .steps .step.completed {
                                        background-color: var(--prelander-primary);
                                        border-color: var(--prelander-primary);
                                    }
                                    .steps .step.current::before {
                                        background-color: var(--prelander-primary);
                                    }
                                    .form_prelander form .form-hor-radiolist .radio-label-inner {
                                        border-color: var(--prelander-primary);
                                        color: var(--prelander-primary);
                                    }
                                    .form_prelander form .form-hor-radiolist .radio-label input:checked ~ .radio-label-inner {
                                        background-color: var(--prelander-primary);
                                        border-color: var(--prelander-primary);
                                    }
                                </style>
                            {% endif %}
                            <div class="absolute inset-x-0 bottom-0 py-1.5 bg-gray-100">
                                <div class="flex px-5 md:px-12 {{ veiligeVerbinding ? 'justify-between' : 'justify-center' }} md:items-center">
                                    {% if veiligeVerbinding %}
                                        <div class="flex space-x-2 items-center justify-center md:justify-start mb-2 md:mb-0">
                                            <div class="w-5 h-5 rounded-full bg-gray-300 flex items-center justify-center">
                                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-3 h-3 text-white">
                                                    <path fill-rule="evenodd" d="M12 1.5a5.25 5.25 0 00-5.25 5.25v3a3 3 0 00-3 3v6.75a3 3 0 003 3h10.5a3 3 0 003-3v-6.75a3 3 0 00-3-3v-3c0-2.9-2.35-5.25-5.25-5.25zm3.75 8.25v-3a3.75 3.75 0 10-7.5 0v3h7.5z" clip-rule="evenodd" />
                                                </svg>
                                            </div>
                                            <span class="text-gray-800 text-center text-xs md:text-base">Veilige verbinding</span>
                                        </div>
                                    {% endif %}

                                    {% if textUnderForm %}
                                        <p class="text-gray-800 text-center text-xs md:text-base ">{{ textUnderForm }}</p>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {% if not disable_header_illustration %}
                    {% if header_illustration %}
                        <img src="{{ header_illustration | media }}" alt="" class="mt-16 md:hidden">
                    {% else %}
                        <img src="{{ 'assets/img/header-illustration-houses.png'|theme }}" alt="Illustratie met huisjes" class="mt-16 md:hidden">
                    {% endif %}
                {% endif %}

            </div>
        </header>

        <main>

            {% page  %}

            {repeater tab="Content" name="flexible" prompt="Blok toevoegen" groups="$/../themes/vrijblijvendeofferte/repeaters/sections.yaml" displayMode="builder" titleFrom="name"}
                {% partial "section/" ~ fields._group ~ ".htm" fields=fields %}
            {/repeater}

            <div class="bg-gray-100">
            <div class="container">
            {% if not hide_reviews %}
                {% if review_type == "custom" %}
                    {% partial 'klantenvertellenFormFeed/custom' review_left_text=review_left_text review_middle_text=review_middle_text review_right_text=review_right_text review_left_name=review_left_name review_middle_name=review_middle_name review_right_name=review_right_name review_left_date=review_left_date review_middle_date=review_middle_date review_right_date=review_right_date review_left_rating=review_left_rating review_middle_rating=review_middle_rating review_right_rating=review_right_rating custom_rating_score=custom_rating_score custom_rating_text=custom_rating_text %}
                {% else %}
                    {% component 'klantenvertellenFormFeed' %}
                {% endif %}
            {% endif %}
            </div>
            </div>
        </main>

        {repeater tab="Scripts" name="page_scripts_head" label="Scripts in de head" prompt="Script toevoegen" span="row" spanClass="col-sm-4"}
            {variable name="date" label="Datum toevoeging" tab="Scripts" type="datepicker" mode="date"}{/variable}
            {variable name="script" label="Scripts in head" tab="Scripts" type="codeeditor" size="huge" language"javascript"}{/variable}
        {/repeater}
        {repeater tab="Scripts" name="page_scripts_body_top" label="Scripts in de body (boven)" prompt="Script toevoegen" span="row" spanClass="col-sm-4"}
            {variable name="date" label="Datum toevoeging" tab="Scripts" type="datepicker" mode="date"}{/variable}
            {variable name="script" label="Scripts in body top" tab="Scripts" type="codeeditor" size="huge" language"javascript"}{/variable}
        {/repeater}
        {repeater tab="Scripts" name="page_scripts_body_bottom" label="Scripts in de body (onder)" prompt="Script toevoegen" span="row" spanClass="col-sm-4"}
            {variable name="date" label="Datum toevoeging" tab="Scripts" type="datepicker" mode="date"}{/variable}
            {variable name="script" label="Scripts in body bottom" tab="Scripts" type="codeeditor" size="huge" language"javascript"}{/variable}
        {/repeater}

        {variable name="review_type" type="dropdown" options="normal:Klanten vertellen|custom:Dynamisch" label="Review weergave" span="full" tab="Reviews"}{/variable}

        {variable name="review_left_text" type="textarea" label="Review links tekst" span="row" spanClass="col-sm-4" tab="Reviews"}{/variable}
        {variable name="review_middle_text" type="textarea" label="Review midden tekst" span="row" spanClass="col-sm-4" tab="Reviews"}{/variable}
        {variable name="review_right_text" type="textarea" label="Review rechts tekst" span="row" spanClass="col-sm-4" tab="Reviews"}{/variable}
        {variable name="review_left_name" type="text" label="Review links naam" span="row" spanClass="col-sm-4" tab="Reviews"}{/variable}
        {variable name="review_middle_name" type="text" label="Review midden naam" span="row" spanClass="col-sm-4" tab="Reviews"}{/variable}
        {variable name="review_right_name" type="text" label="Review rechts naam" span="row" spanClass="col-sm-4" tab="Reviews"}{/variable}
        {variable name="review_left_date" type="datepicker" mode="date" label="Review links datum" span="row" spanClass="col-sm-4" tab="Reviews"}{/variable}
        {variable name="review_middle_date" type="datepicker" mode="date" label="Review midden datum" span="row" spanClass="col-sm-4" tab="Reviews"}{/variable}
        {variable name="review_right_date" type="datepicker" mode="date" label="Review rechts datum" span="row" spanClass="col-sm-4" tab="Reviews"}{/variable}
        {variable name="review_left_rating" type="dropdown" label="Review links waardering" span="row" spanClass="col-sm-4" tab="Reviews" options="1|2|3|4|5"}{/variable}
        {variable name="review_middle_rating" type="dropdown" label="Review midden waardering" span="row" spanClass="col-sm-4" tab="Reviews" options="1|2|3|4|5"}{/variable}
        {variable name="review_right_rating" type="dropdown" label="Review rechts waardering" span="row" spanClass="col-sm-4" tab="Reviews" options="1|2|3|4|5"}{/variable}

        {variable name="custom_rating_score" type="text" label="Eigen cijfer midden" span="left" placeholder="8.9" tab="Reviews"}{/variable}
        {variable name="custom_rating_text" type="text" label="Eigen tekst midden" span="left" tab="Reviews" placeholder="97% van onze klanten beveelt ons aan."}{/variable}

        {% partial "page/footer" %}

        {% if not this.theme.hide_cookiebanner %}{% component 'cookiesBar' %}{% endif %}

        {% partial "site/foot" %}

        {% partial "site/scripts_body_bottom" %}


        {% scripts %}
        {% framework extras %}

         <script>
            var lazyLoadInstance = new LazyLoad({});
        </script>
    </body>
</html>
