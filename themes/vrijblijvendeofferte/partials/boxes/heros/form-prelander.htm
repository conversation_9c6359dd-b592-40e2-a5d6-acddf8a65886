<header class="pt-9 md:pt-12 lg:pt-16 bg-secondary-500" {% if box.header_color %}style="background-color: {{ box.header_color }};"{% endif %}>
    <div class="container">
        <div class="font-bold tracking-tight leading-normal custom_heading">{{ box.page_title|raw }}</div>
        <div class="w-14 h-1 bg-primary-500 my-3 md:my-6"></div>

        <div class="grid md:grid-cols-10">
            <div class="md:col-span-6 md:pr-16">
                <div class="md:flex md:flex-col md:h-full">
                    <div class="text-xl mb-12 lg:mb-0">
                        <div class="text-white basic_content text-xl md:text-2xl">{{ box.header_text|raw }}</div>
                        <div class="text-white mt-4 md:mt-6 text-xl md:text-2xl">{{ box.header_text_intro|raw }}</div>
                        <div class="hidden md:flex md:justify-end">
                            {# <img src="{{ 'assets/img/arrow_header_prelander.png'|theme }}" alt="" class="rotate-180 ml-16 mt-8 md:rotate-0 md:ml-0 md:mt-0"> #}
                            <svg width="107px" height="31px" viewBox="0 0 107 31" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" class="rotate-180 ml-16 mt-8 md:rotate-0 md:ml-0 md:mt-0" style="color: {{ box.arrow_color }};">
                                <g id="Page-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" stroke-linecap="round">
                                    <g id="Prelander_OV_desktop_07-12-22" transform="translate(-723.000000, -600.000000)" stroke="currentColor" stroke-width="2">
                                        <g id="Hero-" transform="translate(0.000000, 192.000000)">
                                            <g id="Pijl" transform="translate(778.006012, 424.498722) scale(1, -1) rotate(-168.000000) translate(-778.006012, -424.498722) translate(724.999866, 413.998722)">
                                                <g id="Group-9" transform="translate(0.000000, 0.000000)">
                                                    <path d="M106.012293,2.84217094e-14 C96.7805153,12.4934872 81.6000933,19.398895 60.4710267,20.7162232 C28.7774269,22.6922155 10.3665658,13.9139417 2.01229284,7.86061113" id="Path-4"></path>
                                                    <polyline id="Path-6" stroke-linejoin="round" transform="translate(8.173184, 10.998406) rotate(-15.000000) translate(-8.173184, -10.998406) " points="1.54688021 17.8474616 1.55960669 4.14935031 14.7994884 8.14768203"></polyline>
                                                </g>
                                            </g>
                                        </g>
                                    </g>
                                </g>
                            </svg>
                        </div>
                    </div>
                    <div class="mt-auto hidden md:block">
                        {% if box.header_illustration %}
                            <img src="{{ box.header_illustration | media }}" alt="{{ box.illustration_title }}">
                        {% else %}
                            <img src="{{ 'assets/img/header-illustration-houses.png'|theme }}" alt="{{ box.illustration_title }}">
                        {% endif %}
                    </div>
                </div>
            </div>
            <div class="md:col-span-4">
                <div class="bg-white rounded-lg shadow-xl p-6 {{ box.veiligeVerbinding ? 'pb-20 md:p-12 md:pb-20' : 'pb-12 md:p-12 md:pb-20' }} overflow-hidden mb-6 md:mb-16 lg:mb-32 relative">



                    <div id="form_default" class="form_prelander mb-6">

                        {% ajaxPartial 'site/dynamic-form' formcode=box.formcode %}

                    </div>



                    {% if box.form_color %}
                        <style>
                            :root {
                                --prelander-primary: {{ box.form_color }}
                            }
                            .steps .step.active, .steps .step.current.active {
                                border-color: var(--prelander-primary);
                                color: var(--prelander-primary);
                            }
                            .steps .step.completed {
                                background-color: var(--prelander-primary);
                                border-color: var(--prelander-primary);
                            }
                            .steps .step.current::before {
                                background-color: var(--prelander-primary);
                            }
                            .form_prelander form .form-hor-radiolist .radio-label-inner {
                                border-color: var(--prelander-primary);
                                color: var(--prelander-primary);
                            }
                            .form_prelander form .form-hor-radiolist .radio-label input:checked ~ .radio-label-inner {
                                background-color: var(--prelander-primary);
                                border-color: var(--prelander-primary);
                            }
                        </style>
                    {% endif %}
                    <div class="absolute inset-x-0 bottom-0 py-1.5 bg-gray-100">
                        <div class="flex px-5 md:px-12 {{ box.veiligeVerbinding ? 'justify-between' : 'justify-center' }} md:items-center">
                            {% if box.veiligeVerbinding %}
                                <div class="flex space-x-2 items-center justify-center md:justify-start mb-2 md:mb-0">
                                    <div class="w-5 h-5 rounded-full bg-gray-300 flex items-center justify-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-3 h-3 text-white">
                                            <path fill-rule="evenodd" d="M12 1.5a5.25 5.25 0 00-5.25 5.25v3a3 3 0 00-3 3v6.75a3 3 0 003 3h10.5a3 3 0 003-3v-6.75a3 3 0 00-3-3v-3c0-2.9-2.35-5.25-5.25-5.25zm3.75 8.25v-3a3.75 3.75 0 10-7.5 0v3h7.5z" clip-rule="evenodd" />
                                        </svg>
                                    </div>
                                    <span class="text-gray-800 text-center text-xs md:text-base">Veilige verbinding</span>
                                </div>
                            {% endif %}

                            {% if box.textUnderForm %}
                                <p class="text-gray-800 text-center text-xs md:text-base ">{{ box.textUnderForm }}</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% if box.header_illustration %}
            <img src="{{ box.header_illustration | media }}" alt="{{ box.illustration_title }}" class="mt-16 md:hidden">
        {% else %}
            <img src="{{ 'assets/img/header-illustration-houses.png'|theme }}" alt="{{ box.illustration_title }}" class="mt-16 md:hidden">
        {% endif %}
    </div>
</header>
