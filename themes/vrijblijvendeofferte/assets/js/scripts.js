
var listSliderNav = new Swiper(".list-slider-nav", {
    spaceBetween: 16,
    slidesPerView: 3,
    direction: "vertical",
    watchSlidesProgress: true,
});
const listSlider = new Swiper('.list-slider', {
    loop: true,
    scrollbar: false,
    pagination: {
        el: '.swiper-pagination',
        clickable: true,
    },
    navigation: {
        nextEl: '#product-slider-next',
        prevEl: '#product-slider-prev',
    },
    thumbs: {
        swiper: listSliderNav,
    },
});

const reviewSlider = new Swiper('.review-slider', {
    loop: true,
    slidesPerView: 1,
    spaceBetween: 30,
    pagination: {
        el: '.review-pagination',
        clickable: true,
    },
    navigation: {
        nextEl: '#review-slider-next',
        prevEl: '#review-slider-prev',
    },
    breakpoints: {
        768: {
            slidesPerView: 2,
            spaceBetween: 30,
        },
        1024: {
            slidesPerView: 3,
            spaceBetween: 30,
        },
    },
});

function setCookie(name,value,days) {
    var expires = "";
    if (days) {
        var date = new Date();
        date.setTime(date.getTime() + (days*24*60*60*1000));
        expires = "; expires=" + date.toUTCString();
    }
    document.cookie = name + "=" + (value || "")  + expires + "; path=/";
}

function getCookie(name) {
    var nameEQ = name + "=";
    var ca = document.cookie.split(';');
    for(var i=0;i < ca.length;i++) {
        var c = ca[i];
        while (c.charAt(0)==' ') { c = c.substring(1,c.length) }
        if (c.indexOf(nameEQ) == 0) { return c.substring(nameEQ.length,c.length) }
    }
    return null;
}

function eraseCookie(name) {
    document.cookie = name +'=; Path=/; Expires=Thu, 01 Jan 1970 00:00:01 GMT;';
}

jQuery(function(){
    $('[data-name="housenumber"]').attr('inputmode', 'numeric');
    $('[data-name="zipcode"]').attr('inputmode', 'numeric').on('keyup', function(){
        var val = $(this).val();
        if (val.length >= 4) {
            $(this).attr('inputmode', 'text');
        } else {
            $(this).attr('inputmode', 'numeric');
        }
    });

    $('.offerLink').on('click', function(e){
        var form = $('#form_default');
        var hasSlogan = $('#form_default').closest('#form_wrapper').hasClass('formHasTitleAbove');
        if ( form.length > 0 ) {
            form.trigger('click');

            var bodyElement = $('#form-2025').closest('body');

            var first_group = form.find('.form-group').first();
            var first_input = first_group.find('input');
            var first_select = first_group.find('select');

            if ( first_input.length > 0 ) {
                first_input.focus();
            } else {
                    first_select.focus();
            }

            if (bodyElement.length > 0) {
                var dataStack = bodyElement._x_dataStack[0];
                dataStack.formFocus = true;
            }

            $('html:not(.oc-boxes-edit-mode), body').animate({
                scrollTop: $("#form_default").offset().top - (hasSlogan ? 50 : 16)
            }, 150);
        } else {
            $('[name="zipcode"]').focus();
        }
    });

    $('#cta_banner .offerLinkWithoutFocus').on('click', function(e){
        var form = $('#form_default');
        var hasSlogan = $('#form_default').closest('#form_wrapper').hasClass('formHasTitleAbove');

        if ( form.length > 0 ) {
            form.trigger('click');
            $('html:not(.oc-boxes-edit-mode), body').animate({
                scrollTop: $("#form_default").offset().top - (hasSlogan ? 50 : 16)
            }, 150);
        }
    });

    $('#landingpage_form').find('[data-name="zipcode"]').on('change', function(){
        var val = $(this).val();
        $(this).val(val.trim());
    });
    $('#landingpage_form').find('[data-name="housenumber"]').on('change', function(){
        var val = $(this).val();
        $(this).val(val.trim());
    });
    $('#landingpage_form').find('[data-name="addition"]').on('change', function(){
        var val = $(this).val();
        $(this).val(val.trim());
    });


    $('li.last_request_container span').text().replace('{x}', '<strong x-text="last_request"></strong>');
    $('li.total_requests_container span').text().replace('{x}', '<strong x-text="total_requests"></strong>');

    $('button.rentedetail_button').on('click', function(e){
        e.preventDefault();
        // $('html, body').animate({
        // scrollTop: $("#rentedetail_content").offset().top
        // }, 300);
        $('html, body').scrollTop($("#rentedetail_content").offset().top)
    });

    $('body').on('click', '.open-form-alert', function(){
        $('.form_alert_button').trigger('click');
        $('html, body').scrollTop($(".form_alert").offset().top -30)
        // $('html, body').animate({
        //   scrollTop: $(".form_alert").offset().top -30
        // }, 300);
    });

    var cookiebarcontent_old = $('#cookies-bar .cookiebar-content .content').html();
    if ( cookiebarcontent_old) {
        var cookiebarcontent_new = cookiebarcontent_old.replace(".</p>", "</p>");
        $('#cookies-bar .cookiebar-content .content').html(cookiebarcontent_new);
    }
});

document.addEventListener('alpine:init', () => {
    Alpine.directive('tooltip', (el, { expression }) => {
        tippy(el, { content: expression })
    })

    Alpine.data('rangeslider', (initialValue = null) => ({
        value: initialValue,
        formatted: null,

        init() {
            let euroLocale = Intl.NumberFormat('nl-NL', { style: 'currency', currency: 'EUR',minimumFractionDigits: 0 });
            return (
                this.formatted = euroLocale.format(this.value)
            )
        }
    }))

    Alpine.data('openClose', () => ({
        open: false,

        toggle() {
            this.open = ! this.open
        },
        close() {
            this.open = false
        }
    }))

    Alpine.data('range', (initialValue = null) => ({
        value: initialValue,
        formatted: null,

        init() {
            let euroLocale = Intl.NumberFormat('nl-NL', { minimumFractionDigits: 0 });
            return (
                this.formatted = euroLocale.format(this.value)
            )
        }
    }))

    Alpine.data('amountFormat', (amount = false) => ({
        amount: amount,
    }))

    Alpine.data('dateOfBirth', () => ({
        value: '',
        init() {
            let picker = flatpickr(this.$refs.picker, {
                dateFormat: 'd-m-Y',
                allowInput: true,
                wrap: true,
                clickOpens: false,
            })

            this.$watch('value', () => picker.setDate(this.value))
        },
    }))

    Alpine.data('cookieTabs', (initialValue = null) => ({
        selectedId: initialValue,
        init() {
            this.$nextTick(() => this.select(this.$id('tab', 2)))
        },
        select(id) {
            this.selectedId = id
        },
        isSelected(id) {
            return this.selectedId === id
        },
        whichChild(el, parent) {
            return Array.from(parent.children).indexOf(el) + 1
        }
    }))

    Alpine.data('tabs', () => ({
        selectedId: null,
        init() {
            this.$nextTick(() => this.select(this.$id('tab', 1)))
        },
        select(id) {
            this.selectedId = id
        },
        isSelected(id) {
            return this.selectedId === id
        },
        whichChild(el, parent) {
            return Array.from(parent.children).indexOf(el) + 1
        }
    }))

    Alpine.data('otpForm', () => ({
        length: 4,
        value: "",
        status: 'start',
        phone: '',
        messagenumber: 0,
        phoneValid: false,
        numberType: null,
        try_again_time: 10,
        timer: null,

        startTimer() {
            if (this.timer) {
                clearInterval(this.timer);
            }

            this.try_again_time = 10;

            this.timer = setInterval(() => {
                if (this.try_again_time > 0) {
                    this.try_again_time--;
                } else {
                    clearInterval(this.timer);
                }
            }, 1000);
        },

        resetTimer() {
            this.startTimer();
        },

        checkLength() {
            if (this.value.length > 3) {
                this.verifyCode();
            }
        },

        verifyCode() {
            this.$refs.verifyOtpCode.click();
        },

        isPhoneValid() {
            var phone = this.$refs.phonenumber;

            if ( phone.classList.contains('valid') ) {
                this.phoneValid = true;
                this.status = 'sms';
            } else {
                this.phoneValid = false;
                this.status = 'start';
            }
        },

        editPhone() {
            this.$refs.phonenumber.disabled = false;
            this.status = 'editphone';
            this.$refs.phonenumber.focus();
            this.$refs.otpChangePrompt.style.display = 'none';
        },

    }));
})
