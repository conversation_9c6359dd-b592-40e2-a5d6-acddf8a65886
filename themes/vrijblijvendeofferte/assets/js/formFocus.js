jQuery(function() {
    var form = $('#form_default form');

    function syncAlpineActiveStep(stepId) {
        var formContainer = document.getElementById('form-2025');
        if (formContainer && formContainer._x_dataStack && formContainer._x_dataStack[0]) {
            var stepNumber = stepId ? parseInt(stepId.replace('form-step-', '')) : 1;
            formContainer._x_dataStack[0].active_step = stepNumber;
        }
    }
    function getStepNumber(step) {
        return step ? parseInt(step.replace('form-step-', '')) : 1;
    }

    $(form).on('click', '.form-btn-next, .form-btn-prev', function(){
        var active_step = $(this).closest('form').data('active_step');
        var stepNumber = getStepNumber(active_step);

        syncAlpineActiveStep(active_step);
        var formContainer = document.getElementById('form-2025');
        // if (formContainer && formContainer._x_dataStack && formContainer._x_dataStack[0] && stepNumber > 1) {
        //     formContainer._x_dataStack[0].formContainerTouchesTop = true;
        // }

        if ( stepNumber > 1 ) {
            if(window.innerWidth < 768) {
                $('#hero-2025-content').hide();
                $('#hero-2025-reviews').hide();
                $('#hero-2025-formcontainer')
                    .removeClass('pt-12 md:pt-16')
                    .addClass('pt-14 md:pt-16 lg:pt-0');

                $('#navbar_topbar').slideUp();
                $('#hero-2025-formcontainer #form_wrapper').removeClass('mb-10');
            }
            $('#hero-2025-image').addClass('hidden lg:flex');
            $('#hero-2025-sidebar').addClass('hidden lg:block');
            $('main > div > .oc-box:not(".oc-box--first")').hide();
        }
    });

    $(form).on('click', '.form-btn-prev, .formFocusClose', function(){
        var active_step = $(this).closest('form').data('active_step');
        var stepNumber = getStepNumber(active_step);
        syncAlpineActiveStep(active_step);

        if ( stepNumber == 1 ) {
            $('#hero-2025-image').removeClass('hidden lg:flex');
            $('#hero-2025-content').show();
            $('#hero-2025-reviews').show();
            $('.oc-box').show();

            $('#hero-2025-formcontainer')
                .addClass('pt-12')
                .addClass('md:pt-16');

            $('#hero-2025-formcontainer #form_wrapper').addClass('mb-10');
            $('#navbar_topbar').slideDown();
        }
    });

    // $('.formFocusClose').on('click', function(e){
    //     e.preventDefault();
    //     syncAlpineActiveStep('1');
    //     var bodyElement = document.body;
    //     if (bodyElement && bodyElement._x_dataStack && bodyElement._x_dataStack[0]) {
    //         bodyElement._x_dataStack[0].formFocus = false;
    //     }
    //     $('.steps .step[data-name="form-step-1"]').trigger('click');
    // });
    function goToFirstStep() {
        syncAlpineActiveStep('1');
        var bodyElement = document.body;
        if (bodyElement && bodyElement._x_dataStack && bodyElement._x_dataStack[0]) {
            bodyElement._x_dataStack[0].formFocus = false;
        }
        $('.steps .step[data-name="form-step-1"]').trigger('click');
    }
});
